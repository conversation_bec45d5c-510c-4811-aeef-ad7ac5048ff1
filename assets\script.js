/**
 * AI Styled Image - Modern Frontend JavaScript
 */

class AIImageTool {
    constructor() {
        this.selectedOverlay = null;
        this.uploadedImage = null;
        this.isProcessing = false;
        this.identity = null;
        this.progressInterval = null;
        
        this.init();
    }

    init() {
        this.loadIdentitySettings();
        this.bindEvents();
        this.updateFormState();
    }

    loadIdentitySettings() {
        if (window.aiImageTool && window.aiImageTool.identity) {
            this.identity = window.aiImageTool.identity;
            this.applyIdentityStyles();
        }
    }

    applyIdentityStyles() {
        if (!this.identity) return;

        const root = document.documentElement;
        
        const colorMap = {
            'primary_color': '--ai-primary',
            'secondary_color': '--ai-secondary',
            'accent_color': '--ai-accent',
            'background_color': '--ai-background',
            'surface_color': '--ai-surface',
            'text_color': '--ai-text',
            'muted_color': '--ai-muted',
            'border_color': '--ai-border'
        };

        Object.entries(colorMap).forEach(([key, cssVar]) => {
            if (this.identity[key]) {
                root.style.setProperty(cssVar, this.identity[key]);
            }
        });

        if (this.identity.border_radius) {
            root.style.setProperty('--ai-radius', `${this.identity.border_radius}px`);
        }

        if (this.identity.font_family) {
            root.style.setProperty('--ai-font', `'${this.identity.font_family}', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif`);
        }
    }

    bindEvents() {
        const uploadZone = document.getElementById('upload-zone');
        const fileInput = document.getElementById('user-image');
        const changeBtn = document.querySelector('.ai-change-btn');

        if (uploadZone) {
            uploadZone.addEventListener('click', () => fileInput?.click());
            uploadZone.addEventListener('dragover', this.handleDragOver.bind(this));
            uploadZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
            uploadZone.addEventListener('drop', this.handleDrop.bind(this));
        }

        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e.target.files));
        }

        if (changeBtn) {
            changeBtn.addEventListener('click', () => {
                this.resetImageUpload();
                fileInput?.click();
            });
        }

        document.addEventListener('click', (e) => {
            const styleCard = e.target.closest('.ai-style-card');
            if (styleCard) {
                this.selectStyle(styleCard.dataset.id);
            }
        });

        const form = document.getElementById('ai-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSubmit();
            });
        }

        const closeResults = document.getElementById('close-results');
        const downloadResult = document.getElementById('download-result');
        const createAnother = document.getElementById('create-another');
        const cancelProcessing = document.getElementById('cancel-processing');

        if (closeResults) {
            closeResults.addEventListener('click', () => this.hideResults());
        }

        if (downloadResult) {
            downloadResult.addEventListener('click', () => this.downloadResult());
        }

        if (createAnother) {
            createAnother.addEventListener('click', () => {
                this.hideResults();
                this.resetForm();
            });
        }

        if (cancelProcessing) {
            cancelProcessing.addEventListener('click', () => this.cancelProcessing());
        }

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideResults();
            }
        });
    }

    selectStyle(styleId) {
        if (!styleId) return;

        this.selectedOverlay = styleId;
        document.getElementById('selected-overlay').value = styleId;

        document.querySelectorAll('.ai-style-card').forEach(card => {
            card.classList.toggle('selected', card.dataset.id === styleId);
        });

        this.updateFormState();
        this.updateStatusText();
    }

    handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.add('drag-over');
    }

    handleDragLeave(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('drag-over');
    }

    handleDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('drag-over');
        
        const files = e.dataTransfer.files;
        this.handleFileSelect(files);
    }

    handleFileSelect(files) {
        if (files.length === 0) return;

        const file = files[0];
        
        if (!file.type.startsWith('image/')) {
            this.showNotification('Please select an image file.', 'error');
            return;
        }

        const maxSize = window.aiImageTool?.maxFileSize || 10485760;
        if (file.size > maxSize) {
            this.showNotification(`File size must be less than ${this.formatFileSize(maxSize)}.`, 'error');
            return;
        }

        this.uploadedImage = file;
        this.showImagePreview(file);
        this.updateFormState();
        this.updateStatusText();
    }

    showImagePreview(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const previewImg = document.getElementById('preview-image');
            const imageName = document.getElementById('image-name');
            const uploadContent = document.querySelector('.ai-upload-content');
            const previewContainer = document.getElementById('image-preview');

            if (previewImg) previewImg.src = e.target.result;
            if (imageName) imageName.textContent = file.name;
            
            if (uploadContent) uploadContent.style.display = 'none';
            if (previewContainer) previewContainer.style.display = 'flex';
        };
        reader.readAsDataURL(file);
    }

    resetImageUpload() {
        const uploadContent = document.querySelector('.ai-upload-content');
        const previewContainer = document.getElementById('image-preview');
        const fileInput = document.getElementById('user-image');

        if (uploadContent) uploadContent.style.display = 'flex';
        if (previewContainer) previewContainer.style.display = 'none';
        if (fileInput) fileInput.value = '';
        
        this.uploadedImage = null;
        this.updateFormState();
        this.updateStatusText();
    }

    updateFormState() {
        const generateBtn = document.getElementById('generate-btn');
        const canGenerate = this.uploadedImage && this.selectedOverlay;
        
        if (generateBtn) {
            generateBtn.disabled = !canGenerate;
        }
    }

    updateStatusText() {
        const statusText = document.getElementById('status-text');
        if (!statusText) return;

        if (this.uploadedImage && this.selectedOverlay) {
            statusText.textContent = 'Ready to generate your AI image!';
            statusText.style.color = 'var(--ai-secondary)';
        } else if (this.uploadedImage) {
            statusText.textContent = 'Now select an architecture style to continue';
            statusText.style.color = 'var(--ai-muted)';
        } else if (this.selectedOverlay) {
            statusText.textContent = 'Upload a photo to continue';
            statusText.style.color = 'var(--ai-muted)';
        } else {
            statusText.textContent = 'Select a photo and style to continue';
            statusText.style.color = 'var(--ai-muted)';
        }
    }

    async handleSubmit() {
        if (!this.uploadedImage || !this.selectedOverlay) {
            this.showNotification('Please upload an image and select a style.', 'error');
            return;
        }

        if (this.isProcessing) return;

        this.isProcessing = true;
        this.showProcessingSection();

        const formData = new FormData();
        formData.append('action', 'ai_styled_process');
        formData.append('user_image', this.uploadedImage);
        formData.append('overlay_id', this.selectedOverlay);
        formData.append('nonce', window.aiImageTool?.nonce || '');

        try {
            const response = await fetch(window.aiImageTool?.ajaxUrl || '/wp-admin/admin-ajax.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showResult(result.data.image_url);
                this.showNotification('Image generated successfully!', 'success');
            } else {
                this.hideProcessingSection();
                this.showNotification(result.data || 'Processing failed. Please try again.', 'error');
            }
        } catch (error) {
            console.error('Processing error:', error);
            this.hideProcessingSection();
            this.showNotification('Processing failed. Please check your connection and try again.', 'error');
        } finally {
            this.isProcessing = false;
        }
    }

    showProcessingSection() {
        const section = document.getElementById('processing-section');
        if (section) {
            section.style.display = 'block';
            section.scrollIntoView({ behavior: 'smooth' });
            this.startProgressAnimation();
        }
    }

    hideProcessingSection() {
        const section = document.getElementById('processing-section');
        if (section) {
            section.style.display = 'none';
            this.stopProgressAnimation();
        }
    }

    startProgressAnimation() {
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        
        if (!progressFill || !progressText) return;

        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 12 + 3;
            if (progress > 95) progress = 95;
            
            progressFill.style.width = `${progress}%`;
            progressText.textContent = `${Math.round(progress)}%`;
            
            if (progress >= 95) {
                clearInterval(interval);
            }
        }, 800);

        this.progressInterval = interval;
    }

    stopProgressAnimation() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
        
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        
        if (progressFill) progressFill.style.width = '0%';
        if (progressText) progressText.textContent = '0%';
    }

    showResult(imageUrl) {
        this.hideProcessingSection();
        
        const resultSection = document.getElementById('results-section');
        const resultImage = document.getElementById('result-image');
        
        if (resultImage) {
            resultImage.src = imageUrl;
            resultImage.onload = () => {
                if (resultSection) {
                    resultSection.style.display = 'block';
                    resultSection.scrollIntoView({ behavior: 'smooth' });
                }
            };
        }
    }

    hideResults() {
        const resultSection = document.getElementById('results-section');
        if (resultSection) {
            resultSection.style.display = 'none';
        }
    }

    cancelProcessing() {
        this.isProcessing = false;
        this.hideProcessingSection();
        this.showNotification('Processing cancelled.', 'info');
    }

    downloadResult() {
        const resultImage = document.getElementById('result-image');
        if (resultImage && resultImage.src) {
            const link = document.createElement('a');
            link.href = resultImage.src;
            link.download = `ai-styled-image-${Date.now()}.jpg`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    resetForm() {
        this.resetImageUpload();
        
        this.selectedOverlay = null;
        document.getElementById('selected-overlay').value = '';
        document.querySelectorAll('.ai-style-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        this.updateFormState();
        this.updateStatusText();
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `ai-notification ai-notification-${type}`;
        notification.innerHTML = `
            <div class="ai-notification-content">
                <span class="ai-notification-message">${message}</span>
                <button type="button" class="ai-notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;
        
        if (!document.querySelector('#ai-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'ai-notification-styles';
            style.textContent = `
                .ai-notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    max-width: 400px;
                    border-radius: var(--ai-radius);
                    box-shadow: var(--ai-shadow-lg);
                    animation: slideIn 0.3s ease-out;
                }
                
                .ai-notification-info {
                    background: var(--ai-secondary);
                    color: var(--ai-surface);
                }
                
                .ai-notification-success {
                    background: var(--ai-accent);
                    color: var(--ai-surface);
                }
                
                .ai-notification-error {
                    background: #ef4444;
                    color: var(--ai-surface);
                }
                
                .ai-notification-content {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 12px 16px;
                    gap: 12px;
                }
                
                .ai-notification-message {
                    flex: 1;
                    font-size: 14px;
                    font-weight: 500;
                }
                
                .ai-notification-close {
                    background: none;
                    border: none;
                    color: inherit;
                    font-size: 18px;
                    font-weight: 700;
                    cursor: pointer;
                    padding: 0;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 4px;
                    opacity: 0.8;
                }
                
                .ai-notification-close:hover {
                    opacity: 1;
                    background: rgba(255, 255, 255, 0.1);
                }
                
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(style);
        }
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

document.addEventListener('DOMContentLoaded', () => {
    new AIImageTool();
});

function handleTouchStart(e) {
    this.startY = e.touches[0].clientY;
}

function handleTouchMove(e) {
    if (!this.startY) return;
    
    const currentY = e.touches[0].clientY;
    const diffY = this.startY - currentY;
    
    if (diffY > 0) {
        e.currentTarget.classList.add('drag-over');
    } else {
        e.currentTarget.classList.remove('drag-over');
    }
}

function handleTouchEnd() {
    this.startY = null;
} 